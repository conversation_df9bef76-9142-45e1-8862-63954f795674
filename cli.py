#!/usr/bin/env python3
"""
Command Line Interface for Target Exploit Workflow
"""

import asyncio
import argparse
import json
import sys
from typing import Optional
from enhanced_workflow import EnhancedTargetExploitWorkflow, WorkflowInput
from target_exploit_workflow import TargetExploitWorkflow
from config import WorkflowConfig


def setup_argparser() -> argparse.ArgumentParser:
    """Setup command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Target Exploit Workflow - OSINT Research Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cli.py --location "Tokyo" --interest "cybersecurity companies"
  python cli.py --location "San Francisco" --interest "AI startups" --enhanced
  python cli.py --location "London" --interest "fintech" --output results.json
  python cli.py --location "Tokyo" --interest "fintech" --enhanced --export-csv --save-persistent
  python cli.py --location "Tokyo" --interest "fintech" --enhanced --export-markdown
  python cli.py --location "Tokyo" --interest "fintech" --enhanced --export-markdown --markdown-filename my_report
  python cli.py --location "Tokyo" --interest "fintech" --enhanced --export-csv --export-markdown --output results.json
  python cli.py --location "Tokyo" --interest "fintech" --enhanced --load-entities persistent_storage/previous_results.csv
        """
    )
    
    parser.add_argument(
        "--location",
        help="Target location for research (e.g., 'Tokyo', 'San Francisco')"
    )

    parser.add_argument(
        "--interest",
        help="Area of interest for research (e.g., 'cybersecurity companies', 'AI startups')"
    )
    
    parser.add_argument(
        "--enhanced",
        action="store_true",
        help="Use enhanced workflow with real API integrations"
    )

    parser.add_argument(
        "--export-csv",
        action="store_true",
        help="Export results to CSV files"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    parser.add_argument(
        "--config-check",
        action="store_true",
        help="Check configuration and exit"
    )

    parser.add_argument(
        "--no-ip-resolution",
        action="store_true",
        help="Disable IP address resolution for subdomains"
    )

    parser.add_argument(
        "--load-entities",
        help="Load entities from a previous CSV export file and merge with new results"
    )

    parser.add_argument(
        "--save-persistent",
        action="store_true",
        help="Save entities to persistent storage for future use"
    )

    parser.add_argument(
        "--export-markdown",
        action="store_true",
        help="Export results to markdown file"
    )

    parser.add_argument(
        "--markdown-filename",
        help="Custom filename for markdown export (without extension)"
    )

    return parser


def check_configuration():
    """Check and display configuration status"""
    print("=== Configuration Check ===")
    
    # Check API keys
    api_keys = {
        "SERP API Key (serpapi.com)": bool(WorkflowConfig.SERP_API_KEY),
        "Serper API Key (serper.dev)": bool(WorkflowConfig.SERPER_API_KEY),
        "OpenAI API Key": bool(WorkflowConfig.OPENAI_API_KEY),
        "Ollama Base URL": bool(WorkflowConfig.OLLAMA_BASE_URL)
    }
    
    print("\nAPI Configuration:")
    for key, configured in api_keys.items():
        status = "✓ Configured" if configured else "✗ Not configured"
        print(f"  {key}: {status}")
    
    print(f"\nSearch Configuration:")
    print(f"  Google Country: {WorkflowConfig.GOOGLE_COUNTRY}")
    print(f"  Google Language: {WorkflowConfig.GOOGLE_LANGUAGE}")

    # Show which search API will be used
    if WorkflowConfig.SERPER_API_KEY:
        print(f"  Active Search Provider: Serper API (serper.dev)")
    elif WorkflowConfig.SERP_API_KEY:
        print(f"  Active Search Provider: SERP API (serpapi.com)")
    else:
        print(f"  Active Search Provider: Mock data (no API keys configured)")

    print(f"\nModel Configuration:")
    for model_type, config in WorkflowConfig.MODELS.items():
        print(f"  {model_type}: {config['name']} ({config['provider']})")

    is_valid = WorkflowConfig.validate_config()
    print(f"\nOverall Status: {'✓ Ready for enhanced mode' if is_valid else '⚠ Will use mock data'}")
    
    return is_valid


async def run_workflow(args) -> Optional[list]:
    """Run the workflow based on arguments"""
    workflow_input = WorkflowInput(
        location=args.location,
        interest=args.interest
    )
    
    if args.enhanced:
        print("Using enhanced workflow with API integrations...")

        # Temporarily disable IP resolution if requested
        from config import WorkflowConfig
        original_dns_enabled = WorkflowConfig.DNS_CONFIG.get("enabled", True)
        if args.no_ip_resolution:
            WorkflowConfig.DNS_CONFIG["enabled"] = False
            print("IP address resolution disabled by user request.")

        workflow = EnhancedTargetExploitWorkflow()
        results = await workflow.execute(workflow_input)

        # Load and merge with existing entities if requested
        if args.load_entities:
            print(f"Loading entities from: {args.load_entities}")
            existing_entities = workflow.load_entities_from_csv(args.load_entities)
            if existing_entities:
                print(f"Loaded {len(existing_entities)} existing entities")
                results = workflow.merge_entities_with_existing(results, existing_entities)
                print(f"Total entities after merge: {len(results)}")
            else:
                print("No existing entities loaded or file not found")

        # Save to persistent storage if requested
        if args.save_persistent:
            persistent_file = workflow.save_entities_to_persistent_storage(results, workflow_input)
            if persistent_file:
                print(f"Entities saved to persistent storage: {persistent_file}")

        # Restore original DNS configuration
        WorkflowConfig.DNS_CONFIG["enabled"] = original_dns_enabled

        # Export to CSV if requested
        if args.export_csv and args.enhanced:
            # Export optimized CSV files (entities and subdomains)
            entities_csv, subdomains_csv = workflow.export_optimized_results_to_csv(results, workflow_input)
            if entities_csv and subdomains_csv:
                print(f"✓ Entities exported to: {entities_csv}")
                print(f"✓ Subdomains exported to: {subdomains_csv}")
                
        # Convert EntityResult objects to dictionaries for JSON serialization
        return [
            {
                "entity": result.entity,
                "link": result.link,
                "domain": result.domain,
                "subdomains": result.subdomains or [],
                "subdomain_ips": [
                    {
                        "domain": info.domain,
                        "ip_address": info.ip_address,
                        "dns_error": info.dns_error
                    }
                    for info in (result.subdomain_ips or [])
                ],
                "location_verification": result.location_verification,
                "subdomain_location_verification": result.subdomain_location_verification
            }
            for result in results
        ]
    else:
        print("Using basic workflow with mock data...")
        workflow = TargetExploitWorkflow()
        results = await workflow.execute(workflow_input)
        return results


def save_results_to_markdown(results: list, args, output_path: str = None):
    """Save results to markdown file"""
    import os
    from datetime import datetime

    try:
        # Generate filename if not provided
        if not output_path:
            if args.markdown_filename:
                filename = f"{args.markdown_filename}.md"
            else:
                # Create timestamp-based filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                location_clean = args.location.replace(" ", "_").replace(",", "")
                interest_clean = args.interest.replace(" ", "_").replace(",", "")
                filename = f"osint_results_{location_clean}_{interest_clean}_{timestamp}.md"

            # Ensure output directory exists
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, filename)

        # Generate markdown content
        markdown_content = _generate_markdown_content(results, args)

        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        print(f"Results exported to markdown: {output_path}")
        return output_path

    except Exception as e:
        print(f"Error saving results to markdown: {e}")
        return None


def _generate_markdown_content(results: list, args) -> str:
    """Generate markdown content from results"""
    from datetime import datetime

    # Header
    content = []
    content.append(f"# OSINT Research Results")
    content.append(f"")
    content.append(f"**Target Location:** {args.location}")
    content.append(f"**Area of Interest:** {args.interest}")
    content.append(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    content.append(f"**Total Entities:** {len(results)}")
    content.append(f"")

    if not results:
        content.append("No results found.")
        return "\n".join(content)

    # Summary statistics
    if args.enhanced and isinstance(results[0], dict):
        total_subdomains = sum(len(result.get('subdomains', [])) for result in results)
        verified_entities = sum(1 for result in results
                              if result.get('location_verification', {}).get('overall_classification') == 'RELEVANT')

        content.append(f"## Summary")
        content.append(f"")
        content.append(f"- **Total Entities Found:** {len(results)}")
        content.append(f"- **Verified Relevant Entities:** {verified_entities}")
        content.append(f"- **Total Subdomains Discovered:** {total_subdomains}")
        content.append(f"- **Average Subdomains per Entity:** {total_subdomains / len(results):.1f}")
        content.append(f"")

    # Table of Contents
    content.append(f"## Table of Contents")
    content.append(f"")
    for i, result in enumerate(results, 1):
        entity_name = result.get('entity', f'Entity {i}') if isinstance(result, dict) else f'Entity {i}'
        content.append(f"{i}. [{entity_name}](#entity-{i})")
    content.append(f"")

    # Detailed results
    content.append(f"## Detailed Results")
    content.append(f"")

    for i, result in enumerate(results, 1):
        if args.enhanced and isinstance(result, dict):
            content.extend(_format_enhanced_entity_markdown(result, i))
        else:
            content.extend(_format_basic_entity_markdown(result, i))
        content.append(f"")

    return "\n".join(content)


def _format_enhanced_entity_markdown(result: dict, index: int) -> list:
    """Format enhanced entity result as markdown"""
    content = []
    entity_name = result.get('entity', f'Entity {index}')

    content.append(f"### Entity {index}: {entity_name} {{#entity-{index}}}")
    content.append(f"")

    # Basic information
    content.append(f"**Primary Link:** {result.get('link', 'N/A')}")
    content.append(f"**Primary Domain:** {result.get('domain', 'N/A')}")
    content.append(f"")

    # Location verification
    location_verification = result.get('location_verification')
    if location_verification:
        classification = location_verification.get('overall_classification', 'UNKNOWN')
        confidence = location_verification.get('confidence', 'unknown')

        # Use emoji based on classification
        status_emoji = {
            'RELEVANT': '✅',
            'PARTIALLY_RELEVANT': '⚠️',
            'NOT_RELEVANT': '❌',
            'UNKNOWN': '❓'
        }.get(classification, '❓')

        content.append(f"**Verification Status:** {status_emoji} {classification} (Confidence: {confidence})")

        if location_verification.get('location_evidence'):
            content.append(f"**Location Evidence:** {location_verification['location_evidence']}")

        if location_verification.get('field_evidence'):
            content.append(f"**Field Evidence:** {location_verification['field_evidence']}")

        content.append(f"")

    # Subdomains
    subdomains = result.get('subdomains', [])
    if subdomains:
        content.append(f"**Verified Subdomains ({len(subdomains)}):**")
        content.append(f"")

        # Create subdomain verification mapping
        subdomain_verification = result.get('subdomain_location_verification', [])
        verification_map = {item.get('domain', ''): item for item in subdomain_verification}

        for subdomain in subdomains:
            verification_data = verification_map.get(subdomain)
            if verification_data:
                classification = verification_data.get('classification', 'UNKNOWN')
                confidence = verification_data.get('confidence', 'unknown')
                purpose = verification_data.get('purpose', '')

                status_emoji = {
                    'FIELD_RELEVANT': '🎯',
                    'ORGANIZATIONAL': '🏢',
                    'TECHNICAL': '⚙️',
                    'NOT_RELEVANT': '❌',
                    'UNKNOWN': '❓'
                }.get(classification, '❓')

                content.append(f"- **{subdomain}** {status_emoji} `{classification}` (Confidence: {confidence})")
                if purpose:
                    content.append(f"  - Purpose: {purpose}")
            else:
                content.append(f"- **{subdomain}**")

        content.append(f"")

    # IP addresses
    subdomain_ips = result.get('subdomain_ips', [])
    if subdomain_ips:
        content.append(f"**Subdomain IP Addresses:**")
        content.append(f"")

        for ip_info in subdomain_ips:
            domain = ip_info.get('domain', 'N/A')
            ip_address = ip_info.get('ip_address')
            dns_error = ip_info.get('dns_error')

            if ip_address:
                content.append(f"- **{domain}** → `{ip_address}`")
            elif dns_error:
                content.append(f"- **{domain}** → ❌ {dns_error}")
            else:
                content.append(f"- **{domain}** → ❓ Unknown status")

        content.append(f"")

    content.append(f"---")

    return content


def _format_basic_entity_markdown(result, index: int) -> list:
    """Format basic entity result as markdown"""
    content = []

    content.append(f"### Entity {index} {{#entity-{index}}}")
    content.append(f"")
    content.append(f"```")
    content.append(f"{result}")
    content.append(f"```")
    content.append(f"")
    content.append(f"---")

    return content


def display_results(results: list, enhanced: bool = False):
    """Display results in a formatted way"""
    print(f"\n=== Workflow Results ({len(results)} items) ===")

    if not results:
        print("No results found.")
        return

    for i, result in enumerate(results, 1):
        if enhanced and isinstance(result, dict):
            print(f"{i}. Entity: {result.get('entity', 'N/A')}")
            print(f"   Link: {result.get('link', 'N/A')}")
            print(f"   Domain: {result.get('domain', 'N/A')}")

            # Display location verification
            location_verification = result.get('location_verification')
            if location_verification:
                classification = location_verification.get('overall_classification', 'UNKNOWN')
                confidence = location_verification.get('confidence', 'unknown')
                print(f"   ✅ Verification: {classification} (confidence: {confidence})")
                if location_verification.get('location_evidence'):
                    print(f"      📍 Location: {location_verification['location_evidence']}")
                if location_verification.get('field_evidence'):
                    print(f"      🏢 Field: {location_verification['field_evidence']}")

            # Display subdomains with location verification
            subdomains = result.get('subdomains', [])
            subdomain_location_verification = result.get('subdomain_location_verification', [])

            if subdomains:
                print(f"   📡 Subdomains ({len(subdomains)}):")

                # Create a mapping of domain to verification data
                verification_map = {}
                if subdomain_location_verification:
                    verification_map = {item.get('domain', ''): item for item in subdomain_location_verification}

                for subdomain in subdomains:
                    verification_data = verification_map.get(subdomain)
                    if verification_data:
                        classification = verification_data.get('classification', 'UNKNOWN')
                        confidence = verification_data.get('confidence', 'unknown')
                        purpose = verification_data.get('purpose', '')
                        print(f"      • {subdomain} [{classification}, {confidence}]")
                        if purpose:
                            print(f"        Purpose: {purpose}")
                    else:
                        print(f"      • {subdomain}")

            # Display subdomain IP addresses
            subdomain_ips = result.get('subdomain_ips', [])
            if subdomain_ips:
                print(f"   🌐 Subdomain IP Addresses:")
                for ip_info in subdomain_ips:
                    domain = ip_info.get('domain', 'N/A')
                    ip_address = ip_info.get('ip_address')
                    dns_error = ip_info.get('dns_error')

                    if ip_address:
                        print(f"      • {domain} → {ip_address}")
                    elif dns_error:
                        print(f"      • {domain} → ❌ {dns_error}")
                    else:
                        print(f"      • {domain} → ❓ Unknown status")
        else:
            print(f"{i}. {result}")
        print()


async def main():
    """Main CLI function"""
    parser = setup_argparser()
    args = parser.parse_args()
    
    # Setup logging level
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Configuration check
    if args.config_check:
        check_configuration()
        return

    # Validate required arguments for workflow execution
    if not args.location or not args.interest:
        print("Error: --location and --interest are required for workflow execution")
        print("Use --config-check to check configuration only")
        sys.exit(1)

    # Display configuration status
    print("=== Target Exploit Workflow ===")
    config_valid = check_configuration()
    
    if args.enhanced and not config_valid:
        print("\nWarning: Enhanced mode requested but configuration incomplete.")
        print("Proceeding with mock data. Set API keys for full functionality.")
    
    print(f"\nTarget Location: {args.location}")
    print(f"Area of Interest: {args.interest}")
    print(f"Mode: {'Enhanced' if args.enhanced else 'Basic'}")
    print()
    
    try:
        # Run workflow
        results = await run_workflow(args)
        
        # Display results
        display_results(results, args.enhanced)
                
        # Export to markdown if requested
        if args.export_markdown:
            save_results_to_markdown(results, args)

            
    except KeyboardInterrupt:
        print("\nWorkflow interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Error running workflow: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
