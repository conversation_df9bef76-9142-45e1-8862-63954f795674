"""
Configuration file for Target Exploit Workflow
"""

import os
from typing import Dict, Any

class WorkflowConfig:
    """Configuration class for the workflow"""
    
    TEST_MODE = False
    
    TEST_CONFIG = {
        'max_results': 5,
        
    }
     
    # Environment variables
    GOOGLE_COUNTRY = os.getenv("google_country", "tw")
    GOOGLE_LANGUAGE = os.getenv("google_lan", "zh-tw")
    
    # API Keys (set these as environment variables)
    SERP_API_KEY = os.getenv("SERP_API_KEY", "d84531eb8ebc062e713a13083721dc2e5e396668ecf4cd29aa30a4fed60605dd")
    SERPER_API_KEY = os.getenv("SERPER_API_KEY", "cf082554c8dc3e327657227479f5f0ac5ac33733")  # Serper.dev API key
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "d84531eb8ebc062e713a13083721dc2e5e396668ecf4cd29aa30a4fed60605dd")
    OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://*************:11434")
    
    # Model configurations
    MODELS = {
        "entity_extraction": {
            "name": "qwen3:32b",
            "provider": "ollama",
            "format": "json"
        },

        "knowledge_extraction": {
            "name": "qwen3:32b",
            "provider": "ollama",
            "format": "json"
        },

        "entity_location_verification": {
            "name": "qwen3:32b",
            "provider": "ollama",
            "format": "json"
        },
        "wikipedia_relevance_verification": {
            "name": "qwen3:32b",
            "provider": "ollama",
            "format": "json"
        },

        "entity_description_generation": {
            "name": "qwen3:32b",
            "provider": "ollama",
            "format": "json"
        }
    }
    
    # Search configurations
    SEARCH_CONFIG = {
        "serp": {
            "num_results": 50,
            "language": GOOGLE_LANGUAGE,
            "country": GOOGLE_COUNTRY,
            "engine": "google"
        },
        "serper": {
            "num_results": 50,
            "language": GOOGLE_LANGUAGE,
            "country": GOOGLE_COUNTRY,
            "base_url": "https://google.serper.dev/search"
        },
        "wikipedia": {
            "language": "zh",
            "max_results": 50  # Maximum Wikipedia search results to process
        }
    }

    # Entity extraction configurations
    ENTITY_EXTRACTION_CONFIG = {
        "max_search_results": 50,  # Maximum search results to process for entity extraction
        "max_wikipedia_results": 50,  # Maximum Wikipedia results to process for entity extraction
        "batch_processing": False,  # Whether to use batch processing (True) or individual processing (False)
        "test_mode": False,  # When True, limits results for faster testing
        "test_max_results": 5  # Maximum results when in test mode
    }
    
    # Workflow node configurations
    NODE_CONFIG = {
        "retry_enabled": False,
        "max_retries": 3,
        "retry_interval": 5000,
        "timeout": 1800
    }

    # DNS resolution configurations
    DNS_CONFIG = {
        "enabled": True,  # Enable IP address resolution for subdomains
        "timeout": 5,     # Timeout in seconds for each DNS query
        "max_concurrent": 10,  # Maximum concurrent DNS queries
        "retry_failed": False  # Whether to retry failed DNS queries
    }
    
    @classmethod
    def get_model_config(cls, model_type: str) -> Dict[str, Any]:
        """Get model configuration by type"""
        return cls.MODELS.get(model_type, cls.MODELS["entity_extraction"])
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration is present"""
        # Check if at least one search API is configured
        search_apis_available = []
        if cls.SERP_API_KEY:
            search_apis_available.append("SERP_API_KEY")
        if cls.SERPER_API_KEY:
            search_apis_available.append("SERPER_API_KEY")

        if not search_apis_available:
            print("Warning: No search API keys configured (SERP_API_KEY or SERPER_API_KEY)")
            print("The workflow will run with mock data.")
            return False
        else:
            print(f"Search APIs available: {', '.join(search_apis_available)}")

        return True


# Prompt templates
PROMPTS = {
    "entity_extraction": """You are an expert OSINT analyst specializing in entity identification and extraction. Your task is to identify and extract all relevant organizational entities related to "{interest}" operating in "{location}" from the provided data sources.

## Data Sources Analysis
{search_results}{llm_knowledge}{wikipedia_text}

## Entity Extraction Criteria
Extract entities that meet ALL of the following criteria:
1. **Field Relevance**: Must be directly related to "{interest}"
2. **Geographic Relevance**: Must operate in, be headquartered in, or serve "{location}"
3. **Current Status**: Must be currently active/operational (exclude historical, defunct, or merged entities)
4. **Specificity**: Must be specific named entities (avoid generic terms like "local companies" or "regional firms")
5. **Active**: Must be an organization that is currently in operation.

## Extraction Guidelines
- **From Search Results**: Analyze titles, snippets, and links for organization names
- **From Wikipedia**: Identify organizations mentioned in the Wikipedia content, exclude **historical** events, news, people, or organizations
- **Exclude**: Generic terms, individual people, places, concepts, historical entities

## Quality Filters
- Remove duplicates (same entity with different naming variations)
- Exclude entities that appear to be:
  * Generic industry terms or concepts
  * Historical or defunct organizations
  * Foreign entities not operating in "{location}"
  * Entities not related to "{interest}"

## Output Format
Return a JSON object with extracted entities as a clean, deduplicated list:

```json
{{"entities": ["Specific Company Name", "Government Agency Name", "Research Institute Name"]}}
```

## Examples of Valid Entities for "{interest}" in "{location}":
- Technology companies: "Microsoft Japan", "Sony Corporation"
- Government agencies: "Ministry of Digital Affairs", "Tokyo Metropolitan Government"
- Educational institutions: "University of Tokyo Computer Science Department"
- Industry associations: "Japan Software Industry Association"
- Research centers: "RIKEN Advanced Intelligence Project"

Extract only the most relevant and specific organizational entities based on the provided data.""",


    "knowledge_extraction": """You are an expert knowledge analyst specializing in "{interest}" industry research. Provide a comprehensive list of important organizational entities related to "{interest}" that operate in or serve "{location}".

## Task Requirements
Generate a knowledge-based list of significant entities in the "{interest}" sector within "{location}" based on your training data and industry knowledge.

## Entity Categories to Include
1. **Major Companies**: Leading corporations, enterprises, and businesses
2. **Government Agencies**: Relevant ministries, departments, regulatory bodies
3. **Educational Institutions**: Universities, research institutes, training centers
4. **Industry Associations**: Professional organizations, trade associations, chambers of commerce
5. **Research Centers**: R&D facilities, innovation hubs, technology centers
6. **Startups & Scale-ups**: Notable emerging companies and unicorns
7. **International Organizations**: Regional offices of global entities

## Quality Standards
- **Specificity**: Provide exact organization names, not generic descriptions
- **Relevance**: Must be directly related to "{interest}" industry
- **Geographic Accuracy**: Must have significant presence or operations in "{location}"
- **Current Status**: Include only active, operational entities
- **Significance**: Focus on well-known, influential, or market-leading organizations

## Output Guidelines
- Prioritize well-established and recognized entities
- Include both large corporations and notable smaller organizations
- Ensure geographic and industry relevance
- Avoid duplicates or very similar entities
- Limit to most important and verifiable entities

## Output Format
```json
{{"entities": ["Specific Organization Name 1", "Specific Organization Name 2", "Specific Organization Name 3"]}}
```

## Example Entities for Technology in Japan:
- "Sony Corporation", "SoftBank Group", "Rakuten", "Nintendo", "Fujitsu", "NTT Data", "Ministry of Economy Trade and Industry", "University of Tokyo", "RIKEN", "Japan Electronics and Information Technology Industries Association"

Provide the most relevant and significant entities for "{interest}" in "{location}".""",


    "entity_location_verification": """You are an OSINT analyst specializing in entity verification and domain identification. Your task is to verify whether each discovered entity is actually relevant to the specified location and industry field, and identify the most authoritative official domain.

## Analysis Context
Target Location: {location}
Target Field: {interest}

## Entity to Verify
Entity: {entity}
Search Results: {search_results}

## Instructions
Analyze the entity based on the search results and determine:
1. **Location Relevance**: Does the entity operate in, have offices in, or serve the target location "{location}"?
2. **Field Relevance**: Does the entity belong to or operate in the specified field "{interest}"?
3. **Entity Type**: Is this a legitimate business/organization or something else (person, place, concept)?
4. **Current Status**: Is this entity currently active/operational (not historical or defunct)?
5. **Authoritative Domain**: Identify the most official, authoritative domain for this entity

## Domain Selection Criteria
From the search results, identify the PRIMARY official website domain by prioritizing:
- **Official company websites** (highest priority)
- **Organizational domains** (.com, .org, country-specific TLDs)
- **Primary business sites** (main corporate websites)

**EXCLUDE these domain types:**
- Social media profiles (LinkedIn, Facebook, Twitter, etc.)
- News websites and articles
- Wikipedia pages
- Job boards and recruitment sites
- Directory listings
- Cloud hosting providers (AWS, Azure, etc.)
- Third-party platforms and marketplaces

## Verification Criteria
- **RELEVANT**: Entity operates in target location AND belongs to target field AND is currently active
- **LOCATION_MISMATCH**: Entity belongs to target field but not in target location
- **FIELD_MISMATCH**: Entity is in target location but not in target field
- **IRRELEVANT**: Entity doesn't match location or field criteria
- **INSUFFICIENT_DATA**: Not enough information to make determination

## Output
```json
{{"entity_verification": {{
    "entity": "{entity}",
    "location_relevance": "relevant|not_relevant|uncertain",
    "field_relevance": "relevant|not_relevant|uncertain",
    "overall_classification": "RELEVANT|LOCATION_MISMATCH|FIELD_MISMATCH|IRRELEVANT|INSUFFICIENT_DATA",
    "confidence": "high|medium|low",
    "location_evidence": "Brief explanation of location relevance",
    "field_evidence": "Brief explanation of field relevance",
    "authoritative_domain": "primary-official-domain.com",
    "domain_confidence": "high|medium|low",
    "domain_reasoning": "Brief explanation of why this domain was selected",
    "recommendation": "include|exclude|needs_review"
}}}}
```""",


    "wikipedia_relevance_verification": """You are an OSINT analyst specializing in content relevance verification. Your task is to verify whether each Wikipedia search result is actually relevant to BOTH the specified location AND interest field.

## STRICT REQUIREMENT
The title must be relevant to BOTH location AND field to be included. Titles relevant to only one criterion should be excluded.

## Task
Analyze the Wikipedia title and determine if it's relevant to:
- **Location**: {location}
- **Interest Field**: {interest}

## Wikipedia Title to Analyze
**Title**: {title}

## Analysis Criteria
1. **Location Relevance**: Does the title relate to, mention, or have significant connection to the specified location "{location}"?
2. **Field Relevance**: Does the title relate to or provide useful information about the specified interest field "{interest}"?
3. **Both Required**: The article must satisfy BOTH criteria to be considered relevant
4. **Information Value**: Does the article provide actionable intelligence for OSINT analysis of {interest} in {location}?

## Classification Guidelines
- **RELEVANT**: Article is useful for BOTH location AND field analysis (ONLY classification that should be included)
- **LOCATION_RELEVANT**: Article is relevant to location but NOT the specific field (EXCLUDE)
- **FIELD_RELEVANT**: Article is relevant to the field but NOT the specific location (EXCLUDE)
- **TANGENTIALLY_RELEVANT**: Article has some connection but limited value (EXCLUDE)
- **NOT_RELEVANT**: Article provides no useful information for the analysis (EXCLUDE)

## Decision Rules
- Only mark as "RELEVANT" if BOTH location_relevance="relevant" AND field_relevance="relevant"
- If only one criterion is met, use appropriate single-criterion classification
- Be strict in your evaluation - when in doubt, exclude rather than include

## Output
```json
{{"wikipedia_verification": {{
    "title": "{title}",
    "location_relevance": "relevant|not_relevant|uncertain",
    "field_relevance": "relevant|not_relevant|uncertain",
    "overall_classification": "RELEVANT|LOCATION_RELEVANT|FIELD_RELEVANT|TANGENTIALLY_RELEVANT|NOT_RELEVANT",
    "confidence": "high|medium|low",
    "location_evidence": "Brief explanation of location relevance",
    "field_evidence": "Brief explanation of field relevance",
    "information_value": "high|medium|low",
    "recommendation": "include|exclude|needs_review",
    "reason": "Brief explanation of the decision"
}}}}
```""",


    "entity_description_generation": """You are an expert OSINT analyst specializing in entity profiling and description generation. Your task is to create a concise, informative description of an entity based on search results and verification data.

## Entity Information
Entity: {entity}
Location: {location}
Interest Field: {interest}

## Search Results
{search_results}

## Verification Data
{verification_data}

## Instructions
Based on the provided search results and verification data, generate a comprehensive but concise description of the entity that includes:

1. **Entity Type**: What kind of organization/company this is
2. **Primary Business/Activities**: What the entity does or specializes in
3. **Location/Geographic Scope**: Where the entity operates or is based
4. **Key Characteristics**: Notable features, size, or distinguishing factors
5. **Current Status**: Whether the entity is active and operational

## Requirements
- Keep the description between 100-200 words
- Focus on factual information from the search results
- Avoid speculation or unverified claims
- Use professional, objective language
- Include relevant details about the entity's role in the specified field and location

## Output Format
```json
{{
    "entity_description": {{
        "entity": "{entity}",
        "description": "Comprehensive description of the entity based on search results and verification",
        "entity_type": "Type of organization (e.g., company, institution, government agency)",
        "primary_activities": "Main business activities or services",
        "geographic_scope": "Geographic areas of operation",
        "status": "Current operational status",
        "confidence": "high|medium|low",
        "sources_used": "Brief note about which search results were most informative"
    }}
}}
```""",


}


# Template configurations
TEMPLATES = {
    "google_results_format": """{% for entry in results %}
Title: {{ entry.title }}
Snippet: {{ entry.snippet }}
Link: {{ entry.link }}
---
{% endfor %}""",

    "simple_text": "{{ text }}",
    
    "array_format": """{% for entry in array %}
{{ entry }}
{% endfor %}"""
}
